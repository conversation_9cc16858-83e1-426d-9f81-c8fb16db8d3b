<template>
  <nav-bar :title="t('plans.planDetail')" :show-bell="false" />
  <div class="content" v-if="loading">
    <!-- 状态卡片骨架屏 -->
    <div class="status-card">
      <div class="status-info">
        <van-skeleton title :row="0" title-width="30%" />
        <div class="status-icon skeleton-icon"></div>
      </div>
    </div>

    <!-- 投放产品骨架屏 -->
    <div class="product-section">
      <div class="product-card">
        <div class="product-image">
          <van-skeleton avatar avatar-size="0.8rem" />
        </div>
        <div class="product-info">
          <van-skeleton title :row="2" />
        </div>
        <div class="product-review">
          <van-skeleton title :row="0" title-width="50%" />
        </div>
      </div>
    </div>

    <!-- 数据概览骨架屏 -->
    <div class="overview-section">
      <div class="overview-header">
        <van-skeleton title :row="0" title-width="40%" />
        <van-skeleton title :row="0" title-width="30%" />
      </div>
      <div class="overview-grid">
        <div class="overview-card" v-for="i in 8" :key="i">
          <div class="overview-main">
            <van-skeleton title :row="0" title-width="50%" />
            <van-skeleton avatar avatar-size="0.22rem" />
          </div>
          <van-skeleton title :row="0" title-width="70%" />
        </div>
      </div>
    </div>

    <!-- 投放规则骨架屏 -->
    <div class="rules-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="rules-content">
        <van-skeleton title :row="3" />
      </div>
    </div>

    <!-- 用户定向骨架屏 -->
    <div class="targeting-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="targeting-content">
        <van-skeleton title :row="3" />
      </div>
    </div>

    <!-- 创建时间骨架屏 -->
    <div class="creation-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <van-skeleton title :row="0" title-width="60%" />
    </div>

    <!-- 投放内容骨架屏 -->
    <div class="ad-content-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="ad-content">
        <van-skeleton title :row="0" title-width="100%" class="skeleton-image" />
        <van-skeleton title :row="2" />
        <div class="ad-links">
          <van-skeleton title :row="3" />
        </div>
      </div>
    </div>

    <!-- 产品信息区域骨架屏 -->
    <div class="product-info-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="product-info-content">
        <div class="product-info-item" v-for="i in 4" :key="i">
          <van-skeleton title :row="0" title-width="30%" />
          <van-skeleton title :row="0" title-width="40%" />
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else-if="plan">
    <!-- 状态卡片 -->
    <div class="status-card" :style="{ backgroundColor: statusBgColor }">
      <div class="status-info">
        <div class="status-text">{{ statusText }}</div>
        <div class="status-icon">
          <van-icon :name="statusIcon" />
        </div>
      </div>
    </div>

    <!-- 投放产品 -->
    <div class="product-section">
      <div class="product-card">
        <div class="product-image">
          <van-image width="0.8rem" height="0.8rem" :src="productIcon" fit="cover" radius="4" />
        </div>
        <div class="product-info">
          <div class="product-name">{{ plan.name }}</div>
          <div class="product-desc">{{ plan.category }}</div>
          <div class="product-company">{{ productDeveloper }}</div>
        </div>
        <div class="product-review">
          <div class="review-link" @click="goToProductDetail">{{ t('plans.viewDetails') }}</div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <div class="overview-header">
        <span class="overview-title">{{ t('plans.dataOverview') }}</span>
        <span class="overview-refresh" @click="fetchPlanData">{{ t('plans.refreshInterval') }} <van-icon
            name="replay" class="refresh-icon" /></span>
      </div>
      <div class="overview-grid">
        <!-- 投放金额 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.investedAmount || 0 }}</span>
            <van-icon name="gold-coin-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.amount') }}</div>
        </div>

        <!-- 投放进度 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.progress || '0%' }}</span>
            <van-icon name="chart-trending-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.placementProgress') }}</div>
        </div>

        <!-- 已消耗 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.consumed || 0 }}</span>
            <van-icon name="refund-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.consumed') }}</div>
        </div>

        <!-- 待消耗 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.pendingConsumption || 0 }}</span>
            <van-icon name="after-sale" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.remaining') }}</div>
        </div>

        <!-- 展示数 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.impressions || 0 }}</span>
            <van-icon name="eye-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.impressions') }}</div>
        </div>

        <!-- 点击数 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.clicks || 0 }}</span>
            <van-icon name="star-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.clicks') }}</div>
        </div>

        <!-- 广告收入 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.income || 0 }}</span>
            <van-icon name="balance-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.adRevenue') }}</div>
        </div>

        <!-- 利润 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.profit || 0 }}</span>
            <van-icon name="balance-pay" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.profit') }}</div>
        </div>
      </div>
    </div>

    <!-- 投放规则 -->
    <div class="rules-section" v-if="plan.promotion">
      <div class="rules-header">{{ t('plans.placementRules') }}</div>
      <div class="rules-content">
        <div class="rule-item" v-for="(value, key) in plan.promotion" :key="key">
          <div class="rule-label">{{ key }}</div>
          <div class="rule-value">{{ value }}</div>
        </div>
      </div>
    </div>

    <!-- 用户定向 -->
    <div class="targeting-section" v-if="plan.audience">
      <div class="targeting-header">{{ t('plans.userTargeting') }}</div>
      <div class="targeting-content">
        <div class="targeting-item" v-for="(value, key) in plan.audience" :key="key">
          <div class="targeting-label">{{ key }}</div>
          <div class="targeting-value">{{ value }}</div>
        </div>
      </div>
    </div>

    <!-- 创建时间 -->
    <div class="creation-section">
      <div class="creation-header">{{ t('plans.createTime') }}</div>
      <div class="creation-content">{{ plan.createTime }}</div>
    </div>

    <!-- 投放内容 -->
    <div class="ad-content-section">
      <div class="ad-content-header">{{ t('plans.adContent') }}</div>
      <div class="ad-content">
        <img :src="plan.appBannerUrl || plan.appIconUrl" alt="Ad Content" class="ad-image" />
        <div class="ad-text">
          {{ plan.description }}
        </div>
        <div class="ad-links">
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.promotionalLink') }}</div>
            <div class="ad-link-value">{{ plan.promotionalLink || 'https://example.com' }}</div>
          </div>
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.adDisplayFormat') }}</div>
            <div class="ad-link-value">Image</div>
          </div>
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.conversionMethod') }}</div>
            <div class="ad-link-value">{{ t('plans.landingPage') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品信息区域 -->
    <div class="product-info-section" v-if="plan.productInfo">
      <div class="product-info-header">产品信息</div>
      <div class="product-info-content">
        <div class="product-info-item">
          <div class="product-info-label">产品名称</div>
          <div class="product-info-value">{{ plan.productInfo.name }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">分类</div>
          <div class="product-info-value">{{ plan.productInfo.category }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">开发者</div>
          <div class="product-info-value">{{ plan.productInfo.developer }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">评分</div>
          <div class="product-info-value">{{ plan.productInfo.rating }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else>
    <div class="error-message">
      <van-empty :description="t('plans.planNotFound')" />
      <van-button type="primary" block @click="$router.push('/plans')">{{ t('plans.backToPlans') }}</van-button>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import {
  Cell as VanCell,
  CellGroup as VanCellGroup,
  Icon as VanIcon,
  Image as VanImage,
  showToast,
  Loading as VanLoading,
  Empty as VanEmpty,
  Button as VanButton,
  Skeleton as VanSkeleton
} from 'vant'
import NavBar from '@/components/NavBar.vue'
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { useTitle } from '@/utils/useTitle'
import { useI18n } from 'vue-i18n'
import { planApi } from '@/api'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const id = computed(() => route.params.id)
// 自动刷新定时器引用
const autoRefreshTimer = ref(null)
// 计划数据
const plan = ref(null)
const loading = ref(true)

// 获取计划数据
const fetchPlanData = async () => {
  loading.value = true
  try {
    const res = await planApi.getPlanDetail(id.value)
    if (res && res.data) {
      plan.value = res.data
    } else {
      showToast(t('plans.planNotFound'))
      router.push('/plans')
    }
  } catch (error) {
    console.error('获取计划数据失败:', error)
    showToast(t('fetchFailed'))
    router.push('/plans')
  } finally {
    loading.value = false
  }
}

// 计算属性：计划状态文本
const statusText = computed(() => {
  if (!plan.value) return ''
  return plan.value.status || t('unknownStatus')
})

// 计算属性：计划状态图标
const statusIcon = computed(() => {
  if (!plan.value) return 'question-o'

  const statusIconMap = {
    '待投放': 'clock-o',
    '匹配中': 'search',
    '投放中': 'play-circle-o',
    '投放失败': 'close-circle-o',
    '投放完成': 'success'
  }

  return statusIconMap[plan.value.status] || 'question-o'
})

// 计算属性：计划状态背景色
const statusBgColor = computed(() => {
  if (!plan.value) return '#1877F2'

  const statusBgColorMap = {
    '待投放': '#1877F2',
    '匹配中': '#1877F2',
    '投放中': '#1877F2',
    '投放失败': '#ff4d4f',
    '投放完成': '#52c41a'
  }

  return statusBgColorMap[plan.value.status] || '#1877F2'
})

// 计算属性：产品图标
const productIcon = computed(() => {
  return plan.value?.productInfo?.icon || plan.value?.appIconUrl || ''
})

// 计算属性：产品开发者
const productDeveloper = computed(() => {
  return plan.value?.productInfo?.developer || t('plans.appDeveloper')
})

// 跳转到产品详情页面
const goToProductDetail = () => {
  if (plan.value) {
    // 使用 productId 字段，如果不存在则使用 plan_code
    const productId = plan.value.productId || plan.value.plan_code
    router.push(`/plans/product/${productId}`)
  }
}

onMounted(() => {
  fetchPlanData()
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
// 监视 planId 变化，重新获取详情
watch(
  () => id.value,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      fetchPlanData()
    }
  },
)

// 开始自动刷新
const startAutoRefresh = () => {
  // 先清除之前的定时器（如果存在）
  stopAutoRefresh()

  // 设置定时刷新
  autoRefreshTimer.value = setInterval(() => {
    fetchPlanData(true)
  }, 30000) // 转换为毫秒
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 设置页面标题
useTitle(() => `${t('plans.planDetail')}${id.value}`)
</script>

<style scoped>
.content {
  padding: 0.16rem;
  background-color: #f5f5f5;
}

/* 状态卡片 */
.status-card {
  background-color: #1877F2;
  border-radius: 0.08rem;
  padding: 0.16rem;
  margin-bottom: 0.16rem;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 0.2rem;
  font-weight: bold;
  color: #fff;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.4rem;
  height: 0.4rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.status-icon .van-icon {
  font-size: 0.24rem;
  color: #fff;
}

/* 投放产品 */
.product-section {
  margin-bottom: 0.16rem;
}

.product-card {
  background-color: #fff;
  border-radius: 0.08rem;
  padding: 0.16rem;
  display: flex;
  align-items: center;
}

.product-image {
  margin-right: 0.12rem;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 0.16rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.04rem;
}

.product-desc {
  font-size: 0.12rem;
  color: #666;
  margin-bottom: 0.04rem;
}

.product-company {
  font-size: 0.12rem;
  color: #999;
}

.product-review {
  color: #1877F2;
  font-size: 0.14rem;
}

.review-link {
  cursor: pointer;
}

.review-link:hover {
  text-decoration: underline;
}

/* 数据概览 */
.overview-section {
  background: #fff;
  padding: 0.18rem 0.12rem 0.12rem 0.12rem;
  border-radius: 0.08rem;
  margin-bottom: 0.16rem;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;
}

.overview-title {
  font-size: 0.16rem;
  font-weight: bold;
}

.overview-refresh {
  font-size: 0.13rem;
  color: #888;
  display: flex;
  align-items: center;
  gap: 0.02rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.14rem;
}

.overview-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 0.12rem;
  padding: 0.1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 0.7rem;
}

.overview-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.08rem;
}

.overview-value {
  font-size: 0.16rem;
  font-weight: bold;
  color: #111;
}

.overview-icon {
  font-size: 0.22rem;
  color: #222;
}

.overview-label {
  font-size: 0.12rem;
  color: #222;
}

.refresh-icon {
  color: #1989fa;
}

/* 投放规则、用户定向、创建时间、广告内容区域 */
.rules-section,
.targeting-section,
.creation-section,
.ad-content-section,
.product-info-section {
  background: #fff;
  padding: 0.16rem;
  border-radius: 0.08rem;
  margin-bottom: 0.16rem;
}

.rules-header,
.targeting-header,
.creation-header,
.ad-content-header,
.product-info-header {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.12rem;
}

.rules-content,
.targeting-content,
.product-info-content {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.rule-item,
.targeting-item,
.ad-link-item,
.product-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.08rem 0;
  border-bottom: 1px solid #f5f5f5;
}

.rule-item:last-child,
.targeting-item:last-child,
.ad-link-item:last-child,
.product-info-item:last-child {
  border-bottom: none;
}

.rule-label,
.targeting-label,
.ad-link-label,
.product-info-label {
  color: #666;
  font-size: 0.14rem;
}

.rule-value,
.targeting-value,
.ad-link-value,
.product-info-value {
  color: #333;
  font-size: 0.14rem;
  font-weight: 500;
  word-break: break-all;
  padding-left: 0.1rem;
}

.creation-content {
  color: #333;
  font-size: 0.14rem;
}

/* 投放内容 */
.ad-content {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.ad-image {
  width: 100%;
  border-radius: 0.08rem;
  margin-bottom: 0.12rem;
}

.ad-text {
  font-size: 0.14rem;
  color: #333;
  line-height: 1.5;
  margin-bottom: 0.12rem;
}

.ad-links {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

/* 错误消息 */
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* 骨架屏样式 */
.skeleton-icon {
  background-color: #f2f3f5;
}

.skeleton-title {
  margin-bottom: 0.12rem;
}

.skeleton-image {
  height: 2rem;
  margin-bottom: 0.12rem;
}

:deep(.van-skeleton__title) {
  height: 0.16rem;
}

:deep(.van-skeleton__row) {
  height: 0.14rem;
  margin-top: 0.08rem;
}

:deep(.van-skeleton__avatar) {
  background-color: #f2f3f5;
}

.overview-card :deep(.van-skeleton) {
  height: 100%;
}

.overview-card :deep(.van-skeleton__avatar) {
  margin-right: 0;
}

.product-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.08rem 0;
  border-bottom: 1px solid #f5f5f5;
}

.product-info-item:last-child {
  border-bottom: none;
}

.product-info-item :deep(.van-skeleton) {
  width: auto;
}
</style>