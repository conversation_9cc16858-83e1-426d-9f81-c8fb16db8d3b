<template>
  <nav-bar :title="t('adCenter')" @click-left="goBack" :useDefaultNavigation="false" />
  <van-pull-refresh v-model="isRefreshing" @refresh="onRefresh" :pull-text="t('pullDown')"
    :loosing-text="t('releaseToRefresh')" :loading-text="t('loading')"
    :success-text="t('refreshSuccess')">
    <div class="content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <template v-if="loading">
          <van-skeleton title avatar row="2" :row-width="['60%', '40%']" />
        </template>
        <template v-else>
          <div class="user-info">
            <div class="user-avatar">
              <van-image round width="0.6rem" height="0.6rem" :src="userStore.userInfo.fb_avatar" alt="User Avatar"
                fit="cover" />
            </div>
            <div class="user-details">
              <div class="user-name-row">
                <div class="user-name">{{ userStore.userInfo.fb_nickname || '<PERSON> Truitt' }}</div>
                <div class="activate-button">
                  <template v-if="userStore.userInfo.vip_level">
                    <div class="vip-badge" @click="router.push('/adcenter/vip')">
                      <van-icon name="diamond-o" class="vip-icon" />
                      <span>VIP {{ userStore.userInfo.vip_level }}</span>
                    </div>
                  </template>
                  <template v-else>
                    <van-button icon="diamond-o" to="/adcenter/vip" size="small" class="facebook-blue-btn">{{
                      t('adcenter.activate')
                    }}</van-button>
                  </template>
                </div>
              </div>
              <div class="user-credit">
                {{ t('adcenter.creditScore') }} <span class="credit-score">{{ userStore.userInfo.creditCode || 0
                }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 账户余额信息 -->
      <div class="account-info">
        <template v-if="loading">
          <div class="account-item skeleton-item">
            <div class="skeleton-icon">
              <!-- 移除内部的骨架屏 -->
            </div>
            <div class="account-content">
              <van-skeleton title :row="1" :row-width="['70%']" />
            </div>
          </div>
          <div class="account-item skeleton-item">
            <div class="skeleton-icon wallet-skeleton-icon">
              <!-- 移除内部的骨架屏 -->
            </div>
            <div class="account-content">
              <van-skeleton title :row="1" :row-width="['70%']" />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="account-item" @click="goToCommission">
            <div class="account-icon">
              <van-icon name="cash-back-record" />
            </div>
            <div class="account-content">
              <div class="account-label">{{ t('adcenter.commission') }}</div>
              <div class="account-value">${{ userStore.userInfo.deposit || '0.00' }}</div>
            </div>
            <van-icon name="arrow" class="account-arrow" />
          </div>
          <div class="account-item" @click="goToWallet">
            <div class="account-icon wallet-icon">
              <van-icon name="balance-o" />
            </div>
            <div class="account-content">
              <div class="account-label">{{ t('adcenter.wallet') }}</div>
              <div class="account-value">${{ userStore.userInfo.balance || '1000.00' }}</div>
            </div>
            <van-icon name="arrow" class="account-arrow" />
          </div>
        </template>
      </div>

      <div class="banner" @click="goToNewcomer">
        <img src="@/assets/image/adcenter/banner.png" alt="Newcomer Support" />
        <div class="title-wrap">
          <div class="title">{{ t('adcenter.banner.title') }}</div>
          <div class="desc">{{ t('adcenter.banner.desc') }}</div>
        </div>
      </div>
      <van-tabs v-model:active="activeTab" class="adcenter-tabs" @click-tab="handleTabClick">
        <van-tab :title="t('adcenter.tabs.today')" />
        <van-tab :title="t('adcenter.tabs.thisWeek')" />
        <van-tab :title="t('adcenter.tabs.thisMonth')" />
        <van-tab :title="t('adcenter.tabs.all')" />
      </van-tabs>
      <div class="overview-section">
        <div class="overview-header">
          <span class="overview-title">{{ t('adcenter.dataOverview') }}</span>
          <span class="overview-refresh" @click="refreshStatsOverview">
            {{ t('adcenter.dataRefresh') }}
            <van-icon name="replay" class="refresh-icon" :class="{ 'refreshing': isDataRefreshing }" />
          </span>
        </div>
        <div class="overview-grid">
          <template v-if="loading || isDataRefreshing">
            <div v-for="i in 8" :key="i" class="overview-card">
              <van-skeleton title :row="1" />
            </div>
          </template>
          <template v-else>
            <!-- 投放金额 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">$ {{ statsData.amount }}</span>
                <van-icon name="gold-coin-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.spendAmount') }}</div>
            </div>

            <!-- 投放订单 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">{{ statsData.orders }}</span>
                <van-icon name="bookmark-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.adOrders') }}</div>
            </div>

            <!-- 已消耗 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">$ {{ statsData.spent }}</span>
                <van-icon name="refund-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.consumed') }}</div>
            </div>

            <!-- 待消耗 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">$ {{ statsData.pending }}</span>
                <van-icon name="after-sale" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.toBeConsumed') }}</div>
            </div>

            <!-- 展示数 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">{{ statsData.impressions }}</span>
                <van-icon name="eye-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.impressions') }}</div>
            </div>

            <!-- 点击数 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">{{ statsData.clicks }}</span>
                <van-icon name="star-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.clicks') }}</div>
            </div>

            <!-- 广告收入 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">$ {{ statsData.revenue }}</span>
                <van-icon name="balance-o" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.adRevenue') }}</div>
            </div>

            <!-- 利润 -->
            <div class="overview-card">
              <div class="overview-main">
                <span class="overview-value">$ {{ statsData.profit }}</span>
                <van-icon name="balance-pay" class="overview-icon" />
              </div>
              <div class="overview-label">{{ t('adcenter.profit') }}</div>
            </div>
          </template>
        </div>
      </div>

      <!-- 功能导航区 -->
      <div class="feature-nav">
        <div class="feature-item" @click="goToAgencyService">
          <div class="feature-icon-wrap">
            <van-icon name="chart-trending-o" class="feature-icon" />
          </div>
          <div class="feature-name">{{ t('adcenter.operation') }}</div>
        </div>
        <div class="feature-item" @click="goToCoupons">
          <div class="feature-icon-wrap">
            <van-icon name="coupon-o" class="feature-icon" />
          </div>
          <div class="feature-name">{{ t('adcenter.coupon') }}</div>
        </div>
        <div class="feature-item" @click="goToFeedback">
          <div class="feature-icon-wrap">
            <van-icon name="edit" class="feature-icon" />
          </div>
          <div class="feature-name">{{ t('adcenter.feedback') }}</div>
        </div>
        <div class="feature-item" @click="goToCustomerService">
          <div class="feature-icon-wrap">
            <van-icon name="service-o" class="feature-icon" />
          </div>
          <div class="feature-name">{{ t('adcenter.service') }}</div>
        </div>
      </div>

      <div class="faq-section">
        <div class="faq-title">{{ t('adcenter.faq') }}</div>
        <div class="faq-list">
          <div class="faq-item" @click="goToFAQ(0)">
            <span>{{ t('adcenter.faqList.whatIsAdUnion') }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div class="faq-item" @click="goToFAQ(1)">
            <span>{{ t('adcenter.faqList.whyCantCancel') }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div class="faq-item" @click="goToFAQ(2)">
            <span>{{ t('adcenter.faqList.onlineRecharge') }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div class="faq-item" @click="goToFAQ(3)">
            <span>{{ t('adcenter.faqList.completionTime') }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div class="faq-item" @click="goToFAQ(4)">
            <span>{{ t('adcenter.faqList.becomeMerchant') }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>
  </van-pull-refresh>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'
import { useI18n } from 'vue-i18n'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import {
  Tabs as VanTabs,
  Tab as VanTab,
  Icon as VanIcon,
  Button as VanButton,
  Image as VanImage,
  Skeleton as VanSkeleton,
  PullRefresh as VanPullRefresh,
} from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getUserInfo, loginAndGetUserInfo } from '@/utils/useUserInfo'
import { getStatsOverview } from '@/api/adCenter'

const { t, locale } = useI18n()
const activeTab = ref(0)
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loading = ref(true)
const isRefreshing = ref(false)
const refreshTimer = ref(null)
const isDataRefreshing = ref(false)
const statsData = ref({
  impressions: 0,
  clicks: 0,
  amount: '0.00',
  orders: 0,
  spent: '0.00',
  pending: '0.00',
  revenue: '0.00',
  profit: '0.00'
})

// 获取数据概览信息
const fetchStatsOverview = async () => {
  try {
    isDataRefreshing.value = true
    // 根据当前选中的标签页获取对应的时间周期
    const periodMap = {
      0: 'today',
      1: 'week',
      2: 'month',
      3: 'all'
    }
    const period = periodMap[activeTab.value] || 'today'

    const res = await getStatsOverview(period)
    if (res && res.data) {
      statsData.value = res.data
    }
  } catch (error) {
    console.error('获取数据概览失败:', error)
  } finally {
    isDataRefreshing.value = false
  }
}

const goBack = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('android') !== -1) {
    // 安卓系统执行的代码
    if (window.AppTweak) {
      window.AppTweak.ClosePage();
    }
  } else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1) {
    // iOS系统执行的代码
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.AppTweak) {
      window.webkit.messageHandlers.AppTweak.postMessage("ClosePage");
    }
  }
}

// 设置自动刷新定时器
const setupRefreshTimer = () => {
  // 清除已有的定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }

  // 设置新的定时器，每30秒刷新一次数据
  refreshTimer.value = setInterval(() => {
    fetchStatsOverview()
  }, 30000) // 30秒
}

// 手动刷新数据概览
const refreshStatsOverview = () => {
  if (isDataRefreshing.value) return // 避免重复请求
  fetchStatsOverview()
}

// 下拉刷新处理函数
const onRefresh = async () => {
  try {
    loading.value = true
    // 调用getUserInfo并传入true参数强制刷新用户信息
    const userInfo = await getUserInfo(true)
    if (!userInfo) {
      console.error('刷新用户信息失败')
    }
    // 同时刷新数据概览
    await fetchStatsOverview()
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    // 完成刷新
    isRefreshing.value = false
    loading.value = false
  }
}

// 初始化数据
onMounted(async () => {
  loading.value = true
  try {
    // 获取URL中的data参数
    const dataParam = route.query.data

    if (!dataParam) {
      // 获取用户信息，优先从store中获取
      const userInfo = await getUserInfo()
      // 如果获取失败，可以添加错误处理
      if (!userInfo) {
        console.error('获取用户信息失败')
      }
    } else {
      // 如果data存在，使用data参数登录并获取用户信息
      // 这里传入data参数，会强制更新用户信息
      const userInfo = await loginAndGetUserInfo(dataParam)
      if (userInfo) {
        locale.value = userInfo.fb_lang
        router.push('/adcenter')
      } else {
        console.error('登录失败或获取用户信息失败')
      }
    }

    if (!userStore.userInfo.auth_code) {
      router.push('/account?url=adcenter')
    }

    // 获取数据概览信息
    await fetchStatsOverview()

    // 设置定时刷新
    setupRefreshTimer()
  } catch (error) {
    console.error('请求数据失败:', error)
  } finally {
    // 无论请求成功或失败，都关闭加载状态
    loading.value = false
  }
})

// 清除定时器
onBeforeUnmount(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})

// 处理标签点击，提高响应速度
const handleTabClick = (tab) => {
  activeTab.value = tab.name || tab.index;
  // 切换标签页后刷新数据
  fetchStatsOverview();
}

// 跳转到FAQ详情页
const goToFAQ = (id) => {
  router.push({
    path: '/adcenter/faq',
    query: { id }
  })
}

// 功能导航跳转
const goToAgencyService = () => {
  router.push('/adcenter/agent')
}

const goToCoupons = () => {
  router.push('/adcenter/coupon')
}

const goToFeedback = () => {
  router.push('/adcenter/feedback')
}

const goToCustomerService = () => {
  router.push('/adcenter/service')
}

const goToWallet = () => {
  router.push('/adcenter/wallet')
}

const goToCommission = () => {
  router.push('/adcenter/commission')
}

const goToNewcomer = () => {
  router.push('/adcenter/newcomer')
}

// 设置页面标题
useTitle(() => t('adCenter'))
</script>

<style scoped>
/* 用户信息卡片样式 */
.user-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.16rem;
  background: #fff;
  margin-bottom: 0.01rem;
  min-height: 0.92rem;
  /* 固定最小高度，防止跳动 */
}

.user-card :deep(.van-skeleton) {
  width: 100%;
  padding: 0;
}

.user-card :deep(.van-skeleton__avatar) {
  width: 0.6rem;
  height: 0.6rem;
}

.user-card :deep(.van-skeleton__title) {
  width: 50% !important;
  height: 0.18rem;
  margin-top: 0;
}

.user-card :deep(.van-skeleton__row) {
  height: 0.12rem;
}

.user-card :deep(.van-skeleton__row:first-child) {
  margin-top: 0.06rem;
  width: 60% !important;
}

.user-card :deep(.van-skeleton__row:last-child) {
  width: 40% !important;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 0.12rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.06rem;
  width: 100%;
}

.user-name {
  font-size: 0.18rem;
  font-weight: bold;
  color: #333;
  margin-right: 0.1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activate-button {
  flex-shrink: 0;
}

.activate-button :deep(.van-button) {
  height: 0.3rem;
  line-height: 0.34rem;
  font-size: 0.14rem;
  border-radius: 0.18rem;
}

.activate-button :deep(.facebook-blue-btn) {
  background-color: #1877f2;
  border-color: #1877f2;
  color: #fff;
}

.vip-badge {
  display: flex;
  align-items: center;
  background-color: #1877f2;
  color: #fff;
  border-radius: 0.18rem;
  padding: 0.04rem 0.12rem;
  font-size: 0.14rem;
  font-weight: bold;
}

.vip-badge .vip-icon {
  font-size: 0.16rem;
  margin-right: 0.04rem;
}

.user-credit {
  display: inline-flex;
  align-items: center;
  font-size: 0.12rem;
  color: #999;
  background-color: #f5f5f5;
  padding: 0.02rem 0.06rem;
  border-radius: 0.1rem;
  width: fit-content;
}

.credit-score {
  margin-left: 0.03rem;
  color: #666;
  font-weight: bold;
}

/* 账户余额信息样式 */
.account-info {
  display: flex;
  justify-content: space-between;
  padding: 0 0.1rem;
  background: #fff;
  margin-bottom: 0.1rem;
  min-height: 0.6rem;
  /* 固定最小高度，防止跳动 */
}

.skeleton-item {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  background: #fff;
  margin: 0.05rem;
  padding: 0.12rem;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  max-width: calc(50% - 0.15rem);
}

.skeleton-icon {
  width: 0.36rem;
  height: 0.36rem;
  border-radius: 0.08rem;
  background: #f2f3f5;
  /* 骨架屏状态下使用灰色 */
  flex-shrink: 0;
  margin-right: 0.12rem;
  position: relative;
}

.wallet-skeleton-icon {
  background: #f2f3f5;
  /* 骨架屏状态下使用灰色 */
}

.account-info :deep(.van-skeleton) {
  width: 100%;
  padding: 0;
}

.account-info :deep(.van-skeleton__title) {
  width: 100% !important;
  height: 0.14rem;
  margin: 0;
}

.account-info :deep(.van-skeleton__row) {
  height: 0.12rem;
  margin-top: 0.04rem;
}

.account-item {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  background: #fff;
  margin: 0.05rem;
  padding: 0.12rem 0.04rem 0.12rem 0.12rem;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  max-width: calc(50% - 0.15rem);
}

.account-icon {
  width: 0.36rem;
  height: 0.36rem;
  border-radius: 0.08rem;
  background: #1877f2;
  /* Facebook蓝色 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.12rem;
}

.wallet-icon {
  background: #1877f2;
  /* Facebook蓝色 */
}

.account-icon .van-icon {
  font-size: 0.24rem;
  color: #fff;
}

.account-content {
  flex: 1;
  overflow: hidden;
}

.account-label {
  font-size: 0.14rem;
  color: #666;
  margin-bottom: 0.04rem;
}

.account-value {
  font-size: 0.12rem;
  font-weight: bold;
  color: #2979ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-arrow {
  color: #999;
  font-size: 0.14rem;
  margin-left: 0.08rem;
}

.banner {
  height: .8rem;
  margin: 0.1rem;
  background: #4e7cdc;
  border-radius: 0.12rem;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.banner .title-wrap {
  color: #fff;
}

.banner .title {
  font-size: 0.22rem;
  font-weight: bold;
}

.banner .desc {
  font-size: 0.14rem;
}

.banner img {
  height: 100%;
}

.adcenter-tabs {
  background: #fff;
  border-bottom: .01rem solid #eee;
}

.overview-section {
  background: #fff;
  padding: 0.18rem 0.12rem 0.12rem 0.12rem;
  border-bottom: .1rem solid #f0f2f5;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;
}

.overview-title {
  font-size: 0.16rem;
  font-weight: bold;
}

.overview-refresh {
  font-size: 0.13rem;
  color: #888;
  display: flex;
  align-items: center;
  gap: 0.02rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.14rem;
}

.overview-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 0.12rem;
  padding: 0.1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 0.7rem;
  /* 固定高度，防止跳动 */
}

.overview-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.08rem;
}

.overview-value {
  font-size: 0.16rem;
  font-weight: bold;
  color: #111;
}

.overview-icon {
  font-size: 0.22rem;
  color: #222;
}

.overview-label {
  font-size: 0.12rem;
  color: #222;
}

/* 数据概览卡片样式 */
.overview-grid :deep(.van-skeleton) {
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.overview-grid :deep(.van-skeleton__title) {
  width: 60% !important;
  height: 0.16rem;
  margin: 0.05rem 0;
}

.overview-grid :deep(.van-skeleton__row) {
  height: 0.12rem;
  margin-top: 0.08rem;
  width: 80% !important;
}

/* 功能导航样式 */
.feature-nav {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: .1rem 0;
  border-bottom: 1px solid #eee;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.feature-icon-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.08rem;
}

.feature-icon {
  font-size: 0.24rem;
  color: #333;
}

.feature-name {
  font-size: 0.1rem;
  color: #333;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.faq-section {
  background: #fff;
  border-radius: 0.08rem;
  padding: 0.16rem 0.12rem 0.12rem 0.12rem;
}

.faq-title {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.08rem;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 0.08rem;
}

.faq-item {
  font-size: 0.14rem;
  color: #666;
  padding: 0.1rem 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.faq-item:last-child {
  border-bottom: none;
}

.arrow-icon {
  color: #999;
  font-size: 0.14rem;
}

.refresh-icon {
  color: #1989fa;
}

.refreshing {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 自定义标签线条样式 */
.adcenter-tabs :deep(.van-tabs__line) {
  width: .8rem !important;
  height: 3px;
  border-radius: 3px;
}
</style>