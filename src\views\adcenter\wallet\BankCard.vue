<template>
  <nav-bar :title="t('bankcard.bankCardTitle')" />
  <div class="content">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="formData.bankName" name="bankName" :label="t('bankcard.bankName')"
          :placeholder="t('bankcard.inputBankName')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.bankName') }) }]" />
        <van-field v-model="formData.beneficiaryName" name="beneficiaryName"
          :label="t('bankcard.beneficiaryName')" :placeholder="t('bankcard.inputBeneficiaryName')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.beneficiaryName') }) }]" />
        <van-field v-model="formData.accountNumber" name="accountNumber" :label="t('bankcard.accountNumber')"
          :placeholder="t('bankcard.inputAccountNumber')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.accountNumber') }) }]" />
        <van-field v-model="formData.abaRoutingNumber" name="abaRoutingNumber"
          :label="t('bankcard.routingNumber')" :placeholder="t('bankcard.inputRoutingNumber')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.routingNumber') }) }]" />
        <van-field v-model="formData.beneficiaryAddress" name="beneficiaryAddress"
          :label="t('bankcard.beneficiaryAddress')" :placeholder="t('bankcard.inputBeneficiaryAddress')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.beneficiaryAddress') }) }]" />
        <van-field v-model="formData.bankAddress" name="bankAddress" :label="t('bankcard.bankAddress')"
          :placeholder="t('bankcard.inputBankAddress')"
          :rules="[{ required: true, message: t('bankcard.fieldRequired', { field: t('bankcard.bankAddress') }) }]" />
      </van-cell-group>
      <div class="submit-btn">
        <van-button round block type="primary" native-type="submit" :loading="submitLoading">{{ t('bankcard.submit') }}</van-button>
      </div>
    </van-form>
    <!-- 交易密码验证组件 -->
    <TransactionPasswordVerify v-model:show="showPasswordVerify" @cancel="onPasswordCancel" @confirm="onPasswordConfirm" ref="passwordVerifyRef" />
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { reactive, ref, onMounted } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useI18n } from 'vue-i18n'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'
import { useNavigation } from '@/utils/navigation'
import { fetchBindingInfo, submitBankCardInfo } from '@/api/wallet'

const { t } = useI18n()
const navigation = useNavigation()
// 表单数据
const formData = reactive({
  type: '0',
  bankName: '',
  beneficiaryName: '',
  accountNumber: '',
  abaRoutingNumber: '',
  beneficiaryAddress: '',
  bankAddress: ''
})

const showPasswordVerify = ref(true)
const passwordVerifyRef = ref(null)
const isLoading = ref(false)
const submitLoading = ref(false)

// 获取银行卡绑定信息
const fetchBankCardInfo = async () => {
  isLoading.value = true
  
  try {
    const res = await fetchBindingInfo({ type: '0' })
    closeToast()
    
    if (res.code === 200 && res.data) {
      // 检查是否有绑定信息且类型为银行卡
      if (res.data.type === '0') { // 类型为0表示银行卡
        // 根据API返回的数据结构填充表单
        formData.bankName = res.data.bankName || ''
        formData.beneficiaryName = res.data.holderName || ''
        formData.accountNumber = res.data.cardNumber || ''
        formData.abaRoutingNumber = res.data.routingNumber || ''
        formData.beneficiaryAddress = res.data.beneficiaryAddress || ''
        formData.bankAddress = res.data.branch || ''
      }
    }
  } catch (error) {
    console.error('获取银行卡信息失败:', error)
    closeToast()
    showToast(t('common.fetchFailed'))
  } finally {
    isLoading.value = false
  }
}

// 提交表单
const onSubmit = async (values) => {
  submitLoading.value = true
  
  try {
    // 将表单数据转换为API所需格式
    const submitData = {
      type: '0', // 银行卡类型为0
      cardNumber: values.accountNumber,
      holderName: values.beneficiaryName,
      bankName: values.bankName,
      branch: values.bankAddress,
      // 可以根据需要添加额外字段
      routingNumber: values.abaRoutingNumber,
      beneficiaryAddress: values.beneficiaryAddress
    }
    
    const res = await submitBankCardInfo(submitData)
    
    if (res.code === 200) {
      showToast(t('bankcard.submitSuccess'))
      // 提交成功后返回上一页
      setTimeout(() => {
        navigation.goBack()
      }, 1500)
    } else {
      showToast(res.message || t('error.general'))
    }
  } catch (error) {
    console.error('提交银行卡信息失败:', error)
    showToast(t('error.general'))
  } finally {
    submitLoading.value = false
  }
}

const onPasswordCancel = () => {
  navigation.goBack()
}

const onPasswordConfirm = (password) => {
  // 使用组件实例调用关闭方法，传入 true 表示是验证成功关闭
  passwordVerifyRef.value.closeVerify(true)
  // 验证成功后获取银行卡绑定信息
  fetchBankCardInfo()
}
</script>

<style scoped>
.submit-btn {
  margin: 0.16rem;
}
</style>