<template>
  <nav-bar title="常见问题" />
  <div class="content">
    <div class="faq-container">
      <div class="faq-item" v-if="currentFaq">
        <div class="faq-question">
          <span>{{ currentFaq.question }}</span>
        </div>
        <div class="faq-answer">
          {{ currentFaq.answer }}
        </div>
      </div>
      <van-empty v-else class="custom-empty" image="search" description="未找到相关问题" />
    </div>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'

import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Empty as VanEmpty } from 'vant'
const route = useRoute()

// 获取路由参数中的问题ID
const faqId = computed(() => {
  return parseInt(route.query.id || '0')
})

// 设置页面标题
useTitle('常见问题详情')

// 常见问题列表
const faqList = [
  {
    id: 0,
    question: '什么是Facebook广告联盟？',
    answer: 'Facebook广告联盟是一个让广告主能够在Facebook平台上投放广告的系统。通过这个联盟，广告主可以接触到全球超过30亿Facebook用户，精准定位目标受众，提高品牌曝光率和转化率。'
  },
  {
    id: 1,
    question: '为什么我们不能取消联盟计划呢？',
    answer: '联盟计划一旦启动就进入了执行阶段，系统会自动分配资源并开始投放。取消计划会影响整个广告系统的运行效率和资源分配，同时也可能违反我们与广告主的协议条款。如果您有特殊情况，请联系客服寻求解决方案。'
  },
  {
    id: 2,
    question: '在线充值问题？',
    answer: '我们支持多种在线充值方式，包括信用卡、PayPal、银行转账和加密货币。充值通常会在24小时内到账，部分方式可即时到账。如果您的充值超过48小时未到账，请提供交易凭证联系客服处理。'
  },
  {
    id: 3,
    question: '完成计划需要多长时间？',
    answer: '计划完成时间取决于多个因素，包括广告预算、目标受众规模、投放频率等。一般来说，小型计划可能在几天内完成，中型计划需要1-2周，大型营销活动可能需要数周到数月不等。我们的系统会根据您设置的参数自动优化投放进度。'
  },
  {
    id: 4,
    question: '我可以成为计划商人吗？',
    answer: '是的，您可以申请成为我们的计划商人。需要满足以下条件：拥有相关行业经验、通过我们的资质审核、完成必要的培训课程。成为计划商人后，您将获得专属的管理后台、更高的佣金比例和专业的技术支持。请在"我的账户"页面申请成为计划商人。'
  },
  {
    id: 5,
    question: '如何提高广告效果？',
    answer: '提高广告效果可以从以下几个方面入手：1. 精准定位目标受众；2. 优化广告创意和文案；3. 设置合理的出价策略；4. 定期分析数据并调整投放策略；5. 测试不同的广告形式；6. 关注受众反馈并及时优化。我们也提供专业的广告优化服务，可以联系客服了解详情。'
  },
  {
    id: 6,
    question: '广告账户被封怎么办？',
    answer: '如果您的广告账户被封，请先检查是否违反了平台政策。常见原因包括：违规内容、异常支付活动、账户安全问题等。您可以通过以下步骤处理：1. 登录广告管理平台查看具体原因；2. 根据提示修改违规内容或提供补充材料；3. 提交申诉；4. 联系客服获取帮助。我们会在3-5个工作日内处理您的申诉请求。'
  }
]

// 根据ID获取当前问题
const currentFaq = computed(() => {
  return faqList.find(item => item.id === faqId.value) || null
})
</script>

<style scoped>
.faq-container {
  padding: 0.16rem;
  height: 100%;
}

.faq-item {
  background: #fff;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  min-height: calc(100vh - 1rem);
  display: flex;
  flex-direction: column;
}

.faq-question {
  padding: 0.2rem;
  font-size: 0.18rem;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
  background-color: #f9f9f9;
}

.faq-answer {
  padding: 0.2rem;
  font-size: 0.15rem;
  color: #333;
  line-height: 1.8;
  flex: 1;
}

.custom-empty {
  min-height: calc(100vh - 1rem);
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
</style>