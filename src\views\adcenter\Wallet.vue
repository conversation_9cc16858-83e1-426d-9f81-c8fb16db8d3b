<template>
  <nav-bar :title="t('wallet.title')" />
  <van-pull-refresh v-model="loading" @refresh="onRefresh" :loading-text="t('refreshing')"
    :pull-text="t('pullToRefresh')">
    <div class="content">
      <div class="asset-overview">
        <div class="total-assets">
          <div class="label">
            {{ t('wallet.totalAssets') }} <van-icon :name="showBalance ? 'eye-o' : 'closed-eye'"
              @click="toggleBalanceVisibility" class="eye-icon" />
          </div>
          <div class="currency">USD</div>
        </div>
        <div class="amount-container">
          <template v-if="isLoading">
            <div class="amount skeleton-block"></div>
          </template>
          <template v-else>
            <div class="amount">{{ showBalance ? `$${formatAmount(walletData.totalAssets)}` : '******' }}</div>
          </template>
        </div>
        <div class="balance-details-container">
          <template v-if="isLoading">
            <div class="balance-details">
              <div class="skeleton-item" v-for="i in 4" :key="i"></div>
            </div>
          </template>
          <template v-else>
            <div class="balance-details">
              <div class="balance-item">
                <span class="balance-label">{{ t('wallet.availableBalance') }}</span>
                <span class="balance-value">{{ showBalance ? `$${formatAmount(walletData.availableBalance)}` : '******'
                }}</span>
              </div>
              <div class="balance-item">
                <span class="balance-label">{{ t('wallet.pendingConsumption') }}</span>
                <span class="balance-value">{{ showBalance ? `$${formatAmount(walletData.pendingConsumption)}` :
                  '******'
                }}</span>
              </div>
              <div class="balance-item">
                <span class="balance-label">{{ t('wallet.pendingSettlement') }}</span>
                <span class="balance-value">{{ showBalance ? `$${formatAmount(walletData.pendingSettlement)}` : '******'
                }}</span>
              </div>
              <div class="balance-item">
                <span class="balance-label">{{ t('wallet.withdrawing') }}</span>
                <span class="balance-value">{{ showBalance ? `$${formatAmount(walletData.pendingWithdrawal)}` : '******'
                }}</span>
              </div>
            </div>
          </template>
        </div>
        <div class="action-buttons">
          <div class="btn withdraw" @click="showWithdrawSheet">{{ t('wallet.withdraw') }}</div>
          <div class="btn deposit" @click="showRechargeSheet">{{ t('wallet.deposit') }}</div>
        </div>
      </div>

      <div class="wallet-menu">
        <div class="menu-item" @click="goToBankCard">
          <van-icon name="card" class="menu-icon" />
          <div class="menu-text">{{ t('wallet.bindBankCard') }}</div>
          <van-icon name="arrow" class="arrow-icon" />
        </div>
        <div class="menu-item" @click="goToCrypto">
          <van-icon name="gold-coin" class="menu-icon" />
          <div class="menu-text">{{ t('wallet.bindCrypto') }}</div>
          <van-icon name="arrow" class="arrow-icon" />
        </div>
        <div class="menu-item" @click="goToSecuritySettings">
          <van-icon name="lock" class="menu-icon" />
          <div class="menu-text">{{ t('wallet.transactionPasswordSettings') }}</div>
          <van-icon name="arrow" class="arrow-icon" />
        </div>
      </div>

      <div class="transactions">
        <div class="section-title">{{ t('wallet.fundDetails') }}</div>
        <div class="divider"></div>

        <template v-if="isTransactionsLoading">
          <div class="skeleton-transaction" v-for="i in 3" :key="i"></div>
        </template>
        <template v-else>
          <van-list v-model:loading="listLoading" :finished="finished" @load="onLoad"
            :finished-text="t('noMoreData')" :loading-text="t('loading')">
            <div class="transaction-item" v-for="item in transactions" :key="item.id"
              @click="showTransactionDetail(item)">
              <div class="transaction-info">
                <div class="transaction-type">{{ getTransactionTypeName(item.type) }}</div>
                <div class="transaction-date">{{ item.create_time }}</div>
              </div>
              <van-icon name="arrow" class="arrow-icon" />
            </div>
          </van-list>
        </template>
      </div>
    </div>
  </van-pull-refresh>

  <!-- 提现提示ActionSheet -->
  <van-action-sheet v-model:show="withdrawVisible" :close-on-click-overlay="true">
    <div class="withdraw-container">
      <div class="withdraw-title">{{ t('wallet.withdraw') }}</div>
      <div class="withdraw-input-area">
        <div class="input-label">{{ t('wallet.withdrawAmount') }}</div>
        <div class="input-container">
          <span class="currency-symbol">$</span>
          <input type="number" v-model="withdrawAmount" class="withdraw-input"
            :placeholder="t('wallet.withdrawAmount')" />
        </div>
        <div class="available-amount">
          <span>{{ t('wallet.availableBalance') }} ${{ formatAmount(walletData.availableBalance) }}</span>
          <span class="all-withdraw" @click="setMaxWithdraw">{{ t('wallet.allWithdraw') }}</span>
        </div>
        <van-radio-group class="bank-container" v-model="withdrawType" direction="horizontal">
          <div class="withdraw-type-title">{{ t('wallet.withdrawType') }}</div>
          <van-radio :disabled="!isSetBank" name="0">{{ t('wallet.bankCard') }}</van-radio>
          <van-radio :disabled="!isSetCrypto" name="1">{{ t('wallet.crypto') }}</van-radio>
        </van-radio-group>
      </div>


      <div class="withdraw-btn" @click="validateWithdraw">{{ t('wallet.withdraw') }}</div>
    </div>
  </van-action-sheet>

  <!-- 存款提示ActionSheet -->
  <van-action-sheet v-model:show="rechargeVisible" :close-on-click-overlay="true">
    <div class="recharge-container">
      <div class="recharge-title">{{ t('wallet.depositTips') }}</div>
      <div class="recharge-banner">
        <img src="@/assets/image/adcenter/wallet/recharge-banner.png" alt="存款提示" />
      </div>
      <div class="recharge-content">
        <p>{{ t('wallet.currencyExchangeIssue') }}</p>
        <p>{{ t('wallet.manualDepositOnly') }}</p>
        <p>{{ t('wallet.contactCustomerService') }}</p>
      </div>
      <div class="contact-btn" @click="contactCustomerService">{{ t('wallet.contactService') }}</div>
    </div>
  </van-action-sheet>

  <!-- 交易详情ActionSheet -->
  <van-action-sheet v-model:show="transactionDetailVisible" :close-on-click-overlay="true">
    <div class="transaction-detail-container">
      <div class="transaction-detail-title">{{ t('wallet.detailTitle') }}</div>
      <div class="transaction-detail-content">
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailId') }}</div>
          <div class="detail-value">{{ currentTransaction.transaction_code }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailTime') }}</div>
          <div class="detail-value">{{ currentTransaction.create_time }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailAmount') }}</div>
          <div class="detail-value" :class="getAmountClass(currentTransaction.amount)">
            {{ formatTransactionAmount(currentTransaction.amount) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.balanceBefore') }}</div>
          <div class="detail-value">${{ currentTransaction.balance_before }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.balanceAfter') }}</div>
          <div class="detail-value">${{ currentTransaction.balance_after }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.status') }}</div>
          <div class="detail-value">{{ currentTransaction.statusText || currentTransaction.status }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailRemark') }}</div>
          <div class="detail-value">{{ currentTransaction.remark || t('wallet.noRemark') }}</div>
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 密码问题弹窗 -->
  <van-action-sheet v-model:show="passwordContactVisible" :close-on-click-overlay="true">
    <div class="password-contact-container">
      <div class="password-contact-title">{{ t('wallet.passwordContactTitle') }}</div>
      <div class="password-contact-banner">
        <img src="@/assets/image/adcenter/wallet/pwdContact.png" alt="密码问题" />
      </div>
      <div class="password-contact-content">
        <p>{{ t('wallet.passwordContactContent1') }}</p>
        <p>{{ t('wallet.passwordContactContent2') }}</p>
      </div>
      <div class="contact-btn" @click="contactCustomerService">{{ t('wallet.contactService') }}</div>
    </div>
  </van-action-sheet>

  <!-- 交易密码验证组件 -->
  <transaction-password-verify v-model:show="showPasswordVerify" @confirm="handlePasswordConfirm"
    @cancel="handlePasswordCancel" />
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'
import { Icon as VanIcon, Empty as VanEmpty } from 'vant'
import { ActionSheet as VanActionSheet, PullRefresh as VanPullRefresh, List as VanList } from 'vant'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { walletApi } from '@/api'
import { showToast, Radio as VanRadio, RadioGroup as VanRadioGroup } from 'vant'
import { useUserStore } from '@/stores/user'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 设置页面标题
useTitle(() => t('wallet.title'))
const withdrawType = ref('bankCard')
const isSetBank = ref(0)
const isSetCrypto = ref(0)
isSetBank.value = userStore.userInfo.isSetBank
isSetCrypto.value = userStore.userInfo.isSetCrypto
// 钱包数据
const walletData = ref({
  totalAssets: 0,
  availableBalance: 0,
  pendingConsumption: 0,
  pendingSettlement: 0,
  pendingWithdrawal: 0
})



// 交易记录
const transactions = ref([])
const currentTransaction = ref({})
const transactionDetailVisible = ref(false)

// 加载状态
const isLoading = ref(true)
const isTransactionsLoading = ref(true)
const loading = ref(false)
const listLoading = ref(false)
const finished = ref(false)
const currentPage = ref(1)
const pageSize = 10

// 处理带逗号的金额字符串转为数字
const parseAmount = (amountStr) => {
  if (!amountStr) return 0
  // 如果是数字类型，直接返回
  if (typeof amountStr === 'number') return amountStr
  // 移除所有逗号，然后转换为浮点数
  return parseFloat(amountStr.toString().replace(/,/g, '')) || 0
}

// 获取钱包信息
const fetchWalletInfo = async () => {
  isLoading.value = true
  try {
    const res = await walletApi.fetchWalletInfo()
    if (res.code === 200 && res.data) {
      // 处理API返回的数据，确保数值类型一致
      walletData.value = {
        totalAssets: parseAmount(res.data.totalAssets),
        availableBalance: parseAmount(res.data.availableBalance),
        pendingConsumption: parseAmount(res.data.pendingConsumption),
        pendingSettlement: parseAmount(res.data.pendingSettlement),
        pendingWithdrawal: parseAmount(res.data.pendingWithdrawal)
      }
    } else {
      showToast(res.message)
    }
  } finally {
    isLoading.value = false
  }
}

// 获取交易记录
const fetchTransactions = async (page = 1, isRefresh = false) => {
  if (page === 1) {
    isTransactionsLoading.value = true
  }

  try {
    const res = await walletApi.fetchWalletTransactions({
      page,
      limit: pageSize
    })

    if (res.code === 200 && res.data) {
      if (isRefresh || page === 1) {
        transactions.value = res.data.items || []
      } else {
        transactions.value = [...transactions.value, ...(res.data.items || [])]
      }

      // 判断是否还有更多数据
      finished.value = !res.data.items || res.data.items.length < pageSize ||
        (res.data.page * pageSize) >= res.data.total
    }
  } finally {
    isTransactionsLoading.value = false
    listLoading.value = false
    loading.value = false
  }
}

// 下拉刷新
const onRefresh = async () => {
  currentPage.value = 1
  finished.value = false
  await Promise.all([
    fetchWalletInfo(),
    fetchTransactions(1, true)
  ])
}

// 加载更多
const onLoad = () => {
  currentPage.value += 1
  fetchTransactions(currentPage.value)
}

// 显示交易详情
const showTransactionDetail = (transaction) => {
  currentTransaction.value = transaction
  transactionDetailVisible.value = true
}

// 组件挂载时获取钱包信息和交易记录
onMounted(() => {
  fetchWalletInfo()
  fetchTransactions()
})

// 控制余额显示状态
const showBalance = ref(true)

// 切换余额显示/隐藏
const toggleBalanceVisibility = () => {
  showBalance.value = !showBalance.value
}

// 存款ActionSheet
const rechargeVisible = ref(false)

// 显示存款ActionSheet
const showRechargeSheet = () => {
  rechargeVisible.value = true
}

// 提现ActionSheet
const withdrawVisible = ref(false)

// 提现相关数据
const withdrawAmount = ref('')
const showPasswordVerify = ref(false)

// 显示提现ActionSheet
const showWithdrawSheet = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else if (!checkIsSetBankCard()) {
    showToast(t('wallet.bindCardFirst'))
  } else {
    withdrawVisible.value = true
  }
}

// 验证提现信息并显示密码验证
const validateWithdraw = () => {
  if (!withdrawAmount.value || parseAmount(withdrawAmount.value) <= 0) {
    showToast(t('wallet.withdrawAmountInvalid'))
    return
  }

  if (parseAmount(withdrawAmount.value) > walletData.value.availableBalance) {
    showToast(t('wallet.withdrawAmountExceed'))
    return
  }

  // 显示密码验证组件
  showPasswordVerify.value = true
}

// 密码验证成功回调
const handlePasswordConfirm = async (password) => {
  try {
    // 调用提现接口
    const res = await walletApi.withdraw({
      amount: parseAmount(withdrawAmount.value),
      payPassword: password,
      type: withdrawType.value
    })

    if (res.code === 200) {
      showToast('提现申请已提交')
      // 关闭密码验证和提现弹窗
      showPasswordVerify.value = false
      withdrawVisible.value = false
      withdrawAmount.value = ''
      // 刷新钱包信息
      fetchWalletInfo()
    } else {
      showToast(res.message || '提现失败')
    }
  } catch (error) {
    showToast('提现失败，请稍后再试')
  }
}

// 密码验证取消回调
const handlePasswordCancel = () => {
  showPasswordVerify.value = false
}

// 设置最大提现金额
const setMaxWithdraw = () => {
  withdrawAmount.value = walletData.value.availableBalance.toString()
}

// 密码问题弹窗相关
const passwordContactVisible = ref(false)

// 显示密码问题弹窗
const showPasswordContactSheet = () => {
  passwordContactVisible.value = true
}

// 联系客服
const contactCustomerService = () => {
  // 这里可以添加联系客服的逻辑
  router.push('/adcenter/service')
  passwordContactVisible.value = false
  rechargeVisible.value = false
}

const checkIsSetPassword = () => {
  return userStore.userInfo.isSetPwd
}

const checkIsSetBankCard = () => {
  return userStore.userInfo.isSetBank || userStore.userInfo.isSetCrypto
}


// 绑定银行卡页面跳转
const goToBankCard = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else {
    router.push('/adcenter/wallet/bank')
  }
}

// 绑定虚拟货币页面跳转
const goToCrypto = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else {
    router.push('/adcenter/wallet/crypto')
  }
}

// 交易密码设置页面跳转
const goToSecuritySettings = () => {
  // 判断用户是否已设置交易密码
  if (checkIsSetPassword() === 0) {
    // 未设置密码，直接跳转到密码设置页面
    router.push('/adcenter/wallet/password')
  } else {
    // 已设置密码，显示交易密码相关问题的弹窗
    showPasswordContactSheet()
  }
}

// 格式化金额显示
const formatAmount = (amount) => {
  // 先确保amount是数字
  const num = parseAmount(amount)
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化交易金额显示（保留原始格式）
const formatTransactionAmount = (amount) => {
  if (!amount) return '$0.00'

  // 如果已经是格式化的字符串，直接返回
  if (typeof amount === 'string') {
    // 确保有$符号
    if (amount.includes('$')) {
      return amount
    }

    // 判断正负号
    if (amount.startsWith('-')) {
      return `-$${amount.substring(1)}`
    } else {
      return `$${amount}`
    }
  }

  // 如果是数字，进行格式化
  const num = parseAmount(amount)
  if (num > 0) {
    return `$${formatAmount(num)}`
  } else if (num < 0) {
    return `-$${formatAmount(Math.abs(num))}`
  }
  return '$0.00'
}

// 获取交易类型名称
const getTransactionTypeName = (type) => {
  const typeMap = {
    'DEPOSIT': t('wallet.systemDeposit'),
    'WITHDRAW': t('wallet.withdraw'),
    'AD_PLACEMENT': t('wallet.adPlacement'),
    'AD_SETTLEMENT': t('wallet.adSettlement'),
    'VIP_ACTIVATION': t('wallet.vipActivation'),
    '申请提现': t('wallet.withdraw'),
    '系统充值': t('wallet.systemDeposit'),
    '广告投放': t('wallet.adPlacement'),
    '广告结算': t('wallet.adSettlement'),
    'VIP激活': t('wallet.vipActivation'),
    'vip_upgrade': t('wallet.vipUpgrade'),
  }
  return typeMap[type] || type
}

// 获取金额类名
const getAmountClass = (amount) => {
  // 处理字符串格式的金额
  if (typeof amount === 'string') {
    if (amount.startsWith('-')) {
      return 'negative-amount'
    } else if (amount !== '0' && amount !== '0.00') {
      return 'positive-amount'
    }
    return ''
  }

  // 处理数字格式的金额
  const num = parseFloat(amount)
  if (num > 0) {
    return 'positive-amount'
  } else if (num < 0) {
    return 'negative-amount'
  }
  return ''
}
</script>

<style scoped>
.asset-overview {
  background-color: #fff;
  border-radius: 0.1rem;
  padding: 0.2rem 0.16rem;
  margin-top: 0.1rem;
  min-height: 2.5rem;
  position: relative;
  overflow: hidden;
  /* 防止内容溢出 */
}

.amount-container {
  height: 0.48rem;
  /* 固定高度，包含上下间距 */
  display: flex;
  align-items: center;
}

.amount {
  font-size: 0.28rem;
  font-weight: bold;
  width: 100%;
}

.balance-details-container {
  min-height: 1.6rem;
  /* 保证最小高度，避免抖动 */
}

/* 骨架屏样式 */
.skeleton-block {
  background: #f2f3f5;
  border-radius: 0.04rem;
  width: 70%;
  height: 0.28rem;
}

.skeleton-container {
  background-color: #f8f8f8;
  border-radius: 0.1rem;
  padding: 0.16rem;
}

.skeleton-item {
  height: 0.2rem;
  background: #e8e8e8;
  border-radius: 0.04rem;
  margin-bottom: 0.1rem;
  width: 100%;
}

.skeleton-transaction {
  height: 0.6rem;
  background: #f2f3f5;
  border-radius: 0.04rem;
  margin-bottom: 0.1rem;
  width: 100%;
}

.total-assets {
  display: flex;
  justify-content: space-between;
}

.label {
  font-size: 0.18rem;
  color: #666;
  font-weight: bold;
}

.eye-icon {
  margin-left: 0.05rem;
  vertical-align: middle;
  font-size: 0.18rem;
  color: #666;
}

.currency {
  font-size: 0.14rem;
  color: #666;
}

.balance-details {
  background-color: #f8f8f8;
  border-radius: 0.1rem;
  padding: 0.16rem;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.1rem;
}

.balance-item:last-child {
  margin-bottom: 0;
}

.balance-label {
  font-size: 0.14rem;
  color: #666;
}

.balance-value {
  font-size: 0.14rem;
  color: #333;
}

.action-buttons {
  display: flex;
  margin-top: 0.2rem;
  gap: 0.16rem;
}

.btn {
  flex: 1;
  height: 0.36rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.22rem;
  font-size: 0.16rem;
}

.withdraw {
  background-color: #fff;
  color: #1877F2;
  border: 1px solid #1877F2;
}

.deposit {
  background-color: #1877F2;
  color: #fff;
}

.wallet-menu {
  background-color: #fff;
  border-radius: 0.1rem;
  margin-top: 0.16rem;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.08rem 0.16rem;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 0.2rem;
  margin-right: 0.1rem;
  color: #1877F2;
}

.menu-text {
  flex: 1;
  font-size: 0.16rem;
}

.arrow-icon {
  color: #999;
}

.transactions {
  background-color: #fff;
  border-radius: 0.1rem;
  padding: 0.16rem;
  margin-top: 0.16rem;
}

.section-title {
  font-size: 0.18rem;
  color: #333;
  font-weight: bold;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0.1rem 0;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.06rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-amount {
  font-size: 0.16rem;
  font-weight: bold;
}

.positive-amount {
  color: #07c160;
}

.negative-amount {
  color: #ee0a24;
}

.transaction-info {
  flex: 1;
}

.transaction-type {
  font-size: 0.14rem;
  color: #333;
  margin-bottom: 0.05rem;
}

.transaction-date {
  font-size: 0.12rem;
  color: #999;
}

.empty-transactions {
  text-align: center;
  padding: 0.3rem 0;
  color: #999;
  font-size: 0.14rem;
}

.load-more {
  text-align: center;
  color: #999;
  font-size: 0.12rem;
  padding: 0.16rem 0;
  background-color: #f5f5f5;
  border-radius: 0.04rem;
  margin-top: 0.16rem;
}

/* 存款提示样式 */
.recharge-container {
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.recharge-title {
  font-size: 0.22rem;
  font-weight: bold;
  margin-bottom: 0.3rem;
}


.recharge-banner img {
  width: 100%;
  height: auto;
}

.recharge-content {
  text-align: center;
  margin-bottom: 0.4rem;
}

.recharge-content p {
  font-size: 0.16rem;
  color: #333;
  line-height: 1.8;
  margin: 0;
}

.contact-btn {
  width: 90%;
  height: 0.44rem;
  background-color: #1877F2;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.22rem;
  font-size: 0.16rem;
}

/* 交易详情样式 */
.transaction-detail-container {
  padding: 0.2rem;
}

.transaction-detail-title {
  font-size: 0.22rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 0.3rem;
}

.transaction-detail-content {
  padding: 0 0.1rem;
}

.detail-item {
  display: flex;
  padding: 0.1rem;
}

.detail-label {
  width: 30%;
  color: #666;
  font-size: 0.16rem;
}

.detail-value {
  flex: 1;
  text-align: right;
  font-size: 0.16rem;
  color: #333;
}

/* 密码问题弹窗样式 */
.password-contact-container {
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.password-contact-title {
  font-size: 0.22rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 0.3rem;
}

.password-contact-banner {
  margin-bottom: 0.3rem;
  text-align: center;
}

.password-contact-banner img {
  width: 80%;
  height: auto;
  border-radius: 0.1rem;
}

.password-contact-content {
  padding: 0 0.1rem;
}

.password-contact-content p {
  font-size: 0.16rem;
  color: #333;
  line-height: 1.8;
  margin-bottom: 0.1rem;
}

.contact-btn {
  background-color: #1877F2;
  color: white;
  text-align: center;
  padding: 0.12rem 0;
  border-radius: 0.05rem;
  font-size: 0.16rem;
  margin-top: 0.2rem;
}

/* 提现提示样式 */
.withdraw-container {
  padding: 0.2rem 0.16rem 0.3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.withdraw-title {
  font-size: 0.18rem;
  font-weight: bold;
  margin-bottom: 0.3rem;
  text-align: center;
}

.withdraw-input-area {
  width: 100%;
  margin-bottom: 0.3rem;
}

.input-label {
  font-size: 0.14rem;
  color: #333;
  margin-bottom: 0.2rem;
  text-align: center;
}

.input-container {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding: 0.1rem 0;
  margin-bottom: 0.1rem;
}

.currency-symbol {
  font-size: 0.24rem;
  font-weight: bold;
  margin-right: 0.1rem;
}

.withdraw-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 0.24rem;
  font-weight: bold;
  text-align: left;
}

.withdraw-input::placeholder {
  color: #ccc;
  font-weight: normal;
  font-size: 0.2rem;
}

.available-amount {
  font-size: 0.14rem;
  color: #666;
  margin-top: 0.1rem;
  display: flex;
  justify-content: space-between;
}

.all-withdraw {
  color: #1877F2;
  cursor: pointer;
}

.bank-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-top: 0.1rem;
}

.withdraw-type-title {
  margin-right: 0.1rem;
}

.withdraw-btn {
  width: 100%;
  height: 0.44rem;
  background-color: #1877F2;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.22rem;
  font-size: 0.16rem;
}
</style>