<template>
  <nav-bar :title="t('crypto.cryptoTitle')" />
  <div class="content">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="formState.currency" readonly name="currency" :label="t('crypto.currencyType')"
          :placeholder="t('crypto.selectCurrency')" right-icon="arrow" @click="showCurrencyPicker = true"
          :rules="[{ required: true, message: t('crypto.currencyRequired') }]" />
        <van-field v-model="formState.address" name="address" :label="t('crypto.address')"
          :placeholder="t('crypto.inputAddress')"
          :rules="[{ required: true, message: t('crypto.addressRequired') }]" />
        <van-field v-model="formState.network" readonly name="network" :label="t('crypto.network')"
          :placeholder="t('crypto.selectNetwork')" right-icon="arrow" @click="showNetworkPicker = true"
          :rules="[{ required: true, message: t('crypto.networkRequired') }]" />
      </van-cell-group>
      <div class="submit-btn">
        <van-button round block type="primary" native-type="submit" :loading="submitLoading">{{ t('crypto.submit') }}</van-button>
      </div>
    </van-form>

    <!-- 货币类型选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker :title="t('crypto.selectCurrency')" :columns="currencyColumns" @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false" />
    </van-popup>

    <!-- 网络选择器 -->
    <van-popup v-model:show="showNetworkPicker" position="bottom">
      <van-picker :title="t('crypto.selectNetwork')" :columns="networkColumns" @confirm="onNetworkConfirm"
        @cancel="showNetworkPicker = false" />
    </van-popup>
    <!-- 交易密码验证组件 -->
    <TransactionPasswordVerify v-model:show="showPasswordVerify" @cancel="onPasswordCancel" @confirm="onPasswordConfirm" ref="passwordVerifyRef" />
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { ref, reactive } from 'vue'
import { showToast, Picker as VanPicker, showLoadingToast, closeToast } from 'vant'
import { useI18n } from 'vue-i18n'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'
import { useNavigation } from '@/utils/navigation'
import { fetchBindingInfo, submitBankCardInfo } from '@/api/wallet'

const { t } = useI18n()
const navigation = useNavigation()

// 表单数据
const formState = reactive({
  currency: '',
  address: '',
  network: ''
})

// 选择器显示状态
const showCurrencyPicker = ref(false)
const showNetworkPicker = ref(false)
const showPasswordVerify = ref(true)
const passwordVerifyRef = ref(null)
const isLoading = ref(false)
const submitLoading = ref(false)

// 货币类型选项
const currencyColumns = [
  { text: 'USDC', value: 'USDC' },
  { text: 'USDT', value: 'USDT' },
  { text: 'PYUSD', value: 'PYUSD' },
  { text: 'ETH', value: 'ETH' }
]

// 网络选项
const networkColumns = [
  { text: 'Ethereum', value: 'Ethereum' },
  { text: 'TRON', value: 'TRON' }
]

// 获取加密货币绑定信息
const fetchCryptoInfo = async () => {
  isLoading.value = true
  
  try {
    const res = await fetchBindingInfo({ type: '1' })
    closeToast()
    
    if (res.code === 200 && res.data) {
      // 检查是否有绑定信息且类型为加密货币
      if (res.data.type === '1') { // 类型为1表示加密货币
        // 将API返回的数据映射到表单
        formState.currency = res.data.currency || ''
        formState.address = res.data.address || ''
        formState.network = res.data.network || ''
      }
    }
  } catch (error) {
    console.error('获取加密货币信息失败:', error)
    closeToast()
    showToast(t('common.fetchFailed'))
  } finally {
    isLoading.value = false
  }
}

// 货币类型选择确认
const onCurrencyConfirm = (value) => {
  formState.currency = value.selectedValues.join(',')
  showCurrencyPicker.value = false
}

// 网络选择确认
const onNetworkConfirm = (value) => {
  formState.network = value.selectedValues.join(',')
  showNetworkPicker.value = false
}

// 密码验证取消
const onPasswordCancel = () => {
  navigation.goBack()
}

// 密码验证成功
const onPasswordConfirm = (password) => {
  // 使用组件实例调用关闭方法，传入 true 表示是验证成功关闭
  passwordVerifyRef.value.closeVerify(true)
  // 验证成功后获取加密货币绑定信息
  fetchCryptoInfo()
}

// 表单提交
const onSubmit = async (values) => {
  submitLoading.value = true
  
  try {
    // 将表单数据转换为API所需格式
    const submitData = {
      type: '1', // 加密货币类型为1
      currency: values.currency,
      address: values.address,
      network: values.network
    }
    
    // 这里使用同一个API接口提交数据，实际项目中可能需要使用专门的加密货币API
    const res = await submitBankCardInfo(submitData)
    
    if (res.code === 200) {
      showToast(t('crypto.submitSuccess'))
      // 提交成功后返回上一页
      setTimeout(() => {
        navigation.goBack()
      }, 1500)
    } else {
      showToast(res.message || t('error.general'))
    }
  } catch (error) {
    console.error('提交加密货币信息失败:', error)
    showToast(t('error.general'))
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.submit-btn {
  margin: 0.16rem;
}
</style>