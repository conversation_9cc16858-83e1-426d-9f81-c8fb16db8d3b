export default {
  wallet: {
    title: 'My Wallet',
    totalAssets: 'Total Assets',
    availableBalance: 'Available Balance',
    pendingConsumption: 'Pending Consumption',
    pendingSettlement: 'Pending Settlement',
    withdrawing: 'Withdrawing',
    withdraw: 'Withdraw',
    deposit: 'Deposit',
    bindBankCard: 'Bind Bank Card',
    bindCrypto: 'Bind Crypto',
    transactionPasswordSettings: 'Transaction Password',
    fundDetails: 'Fund Details',
    systemDeposit: 'System Deposit',
    adSettlement: 'Ad Settlement',
    adPlacement: 'Ad Placement',
    vipActivation: 'VIP Activation',
    vipUpgrade: 'VIP Upgrade',
    noTransactions: 'No transactions',
    balanceBefore: 'Balance Before',
    balanceAfter: 'Balance After',
    status: 'Status',

    // Deposit tips
    depositTips: 'Deposit Tips',
    currencyExchangeIssue: 'Due to currency exchange issues in different countries',
    manualDepositOnly: 'Currently only manual deposit channels are supported',
    contactCustomerService: 'Please contact online customer service for deposit',
    contactService: 'Contact Service',

    // Transaction details
    detailTitle: 'Transaction Details',
    detailId: 'ID',
    detailTime: 'Time',
    detailAmount: 'Amount',
    detailRemark: 'Remark',
    noRemark: 'No remark',
    vipFee: 'VIP Membership Fee',
    
    // Password contact dialog
    passwordContactTitle: 'Transaction Password Issues',
    passwordContactContent1: 'For transaction password related issues',
    passwordContactContent2: 'Please contact customer service',

    // Withdraw
    allWithdraw: 'All Withdraw',
    withdrawAmount: 'Please enter your withdrawal amount',
    withdrawAmountInvalid: 'Please enter a valid withdrawal amount',
    withdrawAmountExceed: 'Withdrawal amount cannot exceed available balance',
    withdrawType: 'Withdrawal Type',
    bindCardFirst: 'Please bind your bank card or crypto first',
    bankCard: 'Bank Card',
    crypto: 'Crypto'
  }
} 